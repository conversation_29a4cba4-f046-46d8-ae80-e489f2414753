import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';

function PromptPreview({ prompt, folderName }) {
  if (!prompt) {
    return null;
  }

  const formatDate = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="border-t border-slate-200/60 bg-gradient-to-b from-white/60 to-slate-50/80">
      <Card className="m-4 shadow-lg border-slate-200/60 bg-white/90 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between">
            <CardTitle className="text-lg font-bold text-slate-800 leading-tight">{prompt.title}</CardTitle>
            {folderName && (
              <Badge variant="outline" className="text-xs bg-slate-50 text-slate-600 border-slate-200 rounded-full px-3 py-1">
                {folderName}
              </Badge>
            )}
          </div>
          {prompt.description && (
            <CardDescription className="text-sm text-slate-600 leading-relaxed mt-2">
              {prompt.description}
            </CardDescription>
          )}
        </CardHeader>

        <CardContent className="pt-0 space-y-5">
          <div>
            <h4 className="text-sm font-semibold text-slate-700 mb-3 flex items-center gap-2">
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              内容预览
            </h4>
            <div className="bg-slate-50 border border-slate-200 rounded-lg p-4 text-sm font-mono text-slate-700 max-h-32 overflow-y-auto whitespace-pre-wrap leading-relaxed shadow-inner">
              {prompt.content}
            </div>
          </div>

          <div className="space-y-3 pt-3 border-t border-slate-200">
            <div className="flex justify-between text-sm">
              <span className="text-slate-500 font-medium">创建时间</span>
              <span className="text-slate-700 font-semibold">{formatDate(prompt.createdAt)}</span>
            </div>

            {prompt.updatedAt && prompt.updatedAt !== prompt.createdAt && (
              <div className="flex justify-between text-sm">
                <span className="text-slate-500 font-medium">更新时间</span>
                <span className="text-slate-700 font-semibold">{formatDate(prompt.updatedAt)}</span>
              </div>
            )}

            {prompt.usageCount > 0 && (
              <div className="flex justify-between text-sm">
                <span className="text-slate-500 font-medium">使用次数</span>
                <span className="text-blue-600 font-bold bg-blue-50 px-2 py-1 rounded-full text-xs">{prompt.usageCount}</span>
              </div>
            )}

            {prompt.lastUsedAt && (
              <div className="flex justify-between text-sm">
                <span className="text-slate-500 font-medium">最后使用</span>
                <span className="text-slate-700 font-semibold">{formatDate(prompt.lastUsedAt)}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default PromptPreview;
