import React, { useEffect, useRef, useState } from 'react';
import PromptItem from './PromptItem';

function PromptList({
  prompts,
  selectedIndex,
  onSelect,
  onInsert,
  getFolderName,
  searchQuery,
  onEdit,
  onDelete,
  onPromptMove
}) {
  const listRef = useRef(null);
  const selectedItemRef = useRef(null);
  const [draggedPrompt, setDraggedPrompt] = useState(null);
  const [dragOverPrompt, setDragOverPrompt] = useState(null);

  // 当选中项改变时，滚动到可见区域
  useEffect(() => {
    if (selectedItemRef.current && listRef.current) {
      const listRect = listRef.current.getBoundingClientRect();
      const itemRect = selectedItemRef.current.getBoundingClientRect();
      
      if (itemRect.top < listRect.top) {
        // 选中项在可视区域上方
        selectedItemRef.current.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start' 
        });
      } else if (itemRect.bottom > listRect.bottom) {
        // 选中项在可视区域下方
        selectedItemRef.current.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'end' 
        });
      }
    }
  }, [selectedIndex]);

  const handleItemClick = (index) => {
    onSelect(index);
  };

  const handleItemDoubleClick = (prompt) => {
    onInsert(prompt);
  };

  // 拖拽事件处理
  const handleDragStart = (prompt, e) => {
    setDraggedPrompt(prompt);
  };

  const handleDragEnd = (prompt, e) => {
    setDraggedPrompt(null);
    setDragOverPrompt(null);
  };

  const handleDragOver = (prompt, e) => {
    if (draggedPrompt && draggedPrompt.id !== prompt.id) {
      setDragOverPrompt(prompt);
    }
  };

  const handleDrop = (draggedId, targetPrompt, e) => {
    const draggedPromptData = prompts.find(p => p.id === draggedId);
    if (draggedPromptData && onPromptMove) {
      onPromptMove(draggedPromptData, targetPrompt);
    }
    setDraggedPrompt(null);
    setDragOverPrompt(null);
  };

  if (prompts.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center max-w-sm">
          {searchQuery ? (
            <>
              <div className="w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mb-6 mx-auto">
                <span className="text-2xl">🔍</span>
              </div>
              <div className="text-xl font-bold text-slate-800 mb-3">未找到匹配的 Prompt</div>
              <div className="text-sm text-slate-500 leading-relaxed">
                尝试使用不同的关键词搜索，或者检查拼写是否正确
              </div>
            </>
          ) : (
            <>
              <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-6 mx-auto">
                <span className="text-2xl">📝</span>
              </div>
              <div className="text-xl font-bold text-slate-800 mb-3">暂无 Prompt</div>
              <div className="text-sm text-slate-500 leading-relaxed">
                点击上方的"添加 Prompt"按钮创建您的第一个 Prompt，开始提升工作效率
              </div>
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto" ref={listRef}>
      {prompts.map((prompt, index) => (
        <PromptItem
          key={prompt.id}
          ref={index === selectedIndex ? selectedItemRef : null}
          prompt={prompt}
          isSelected={index === selectedIndex}
          folderName={getFolderName(prompt.folderId)}
          searchQuery={searchQuery}
          onClick={() => handleItemClick(index)}
          onDoubleClick={() => handleItemDoubleClick(prompt)}
          onEdit={onEdit}
          onDelete={onDelete}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          isDragging={draggedPrompt && draggedPrompt.id === prompt.id}
          isDragOver={dragOverPrompt && dragOverPrompt.id === prompt.id}
        />
      ))}
    </div>
  );
}

export default PromptList;
