<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt 管理器 - 重设计版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #2c3e50;
            line-height: 1.6;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        /* 侧边栏 */
        .sidebar {
            width: 280px;
            background-color: #fff;
            border-right: 1px solid #e8ecf1;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid #e8ecf1;
        }

        .logo {
            font-size: 20px;
            font-weight: 600;
            color: #1a1a1a;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .logo::before {
            content: "📝";
            font-size: 24px;
        }

        .search-box {
            margin: 16px;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 10px 16px 10px 40px;
            border: 1px solid #e8ecf1;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .search-input:focus {
            outline: none;
            border-color: #5b8def;
            box-shadow: 0 0 0 3px rgba(91, 141, 239, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #8b92a3;
        }

        .prompt-list {
            flex: 1;
            overflow-y: auto;
            padding: 8px;
        }

        .prompt-item {
            padding: 16px;
            margin-bottom: 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
        }

        .prompt-item:hover {
            background-color: #f8f9fb;
        }

        .prompt-item.active {
            background-color: #eef3ff;
            border-color: #5b8def;
        }

        .prompt-item-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
            color: #1a1a1a;
        }

        .prompt-item-meta {
            font-size: 12px;
            color: #8b92a3;
            display: flex;
            gap: 12px;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: #f5f7fa;
        }

        .toolbar {
            background-color: #fff;
            padding: 16px 24px;
            border-bottom: 1px solid #e8ecf1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .toolbar-left {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background-color: #5b8def;
            color: white;
        }

        .btn-primary:hover {
            background-color: #4a7dd8;
        }

        .btn-secondary {
            background-color: #f0f2f5;
            color: #2c3e50;
        }

        .btn-secondary:hover {
            background-color: #e3e7ec;
        }

        .content-area {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 380px;
            gap: 24px;
            height: 100%;
        }

        .editor-section {
            background-color: #fff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .section-header {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #1a1a1a;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .form-input, .form-textarea {
            width: 100%;
            padding: 10px 14px;
            border: 1px solid #e8ecf1;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .form-input:focus, .form-textarea:focus {
            outline: none;
            border-color: #5b8def;
            box-shadow: 0 0 0 3px rgba(91, 141, 239, 0.1);
        }

        .properties-panel {
            background-color: #fff;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .property-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f2f5;
        }

        .property-item:last-child {
            border-bottom: none;
        }

        .property-label {
            font-size: 14px;
            color: #2c3e50;
        }

        .property-value {
            font-size: 14px;
            color: #8b92a3;
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background-color: #e8ecf1;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .toggle-switch.active {
            background-color: #5b8def;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background-color: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active::after {
            transform: translateX(20px);
        }

        .tag-input-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 8px;
            border: 1px solid #e8ecf1;
            border-radius: 6px;
            min-height: 42px;
            align-items: center;
        }

        .tag {
            background-color: #eef3ff;
            color: #5b8def;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .tag-remove {
            cursor: pointer;
            font-size: 16px;
            line-height: 1;
        }

        .tag-input {
            border: none;
            outline: none;
            flex: 1;
            min-width: 100px;
            font-size: 14px;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f0f2f5;
        }

        ::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #b8bdc7;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">Prompt 管理器</div>
            </div>
            
            <div class="search-box">
                <span class="search-icon">🔍</span>
                <input type="text" class="search-input" placeholder="搜索 Prompt...">
            </div>

            <div class="prompt-list">
                <div class="prompt-item active">
                    <div class="prompt-item-title">专业邮件回复助手</div>
                    <div class="prompt-item-meta">
                        <span>3次使用</span>
                        <span>2分钟前</span>
                    </div>
                </div>
                <div class="prompt-item">
                    <div class="prompt-item-title">会议纪要模板</div>
                    <div class="prompt-item-meta">
                        <span>12次使用</span>
                        <span>1小时前</span>
                    </div>
                </div>
                <div class="prompt-item">
                    <div class="prompt-item-title">代码审查助手</div>
                    <div class="prompt-item-meta">
                        <span>8次使用</span>
                        <span>今天</span>
                    </div>
                </div>
                <div class="prompt-item">
                    <div class="prompt-item-title">创意写作伙伴</div>
                    <div class="prompt-item-meta">
                        <span>5次使用</span>
                        <span>昨天</span>
                    </div>
                </div>
            </div>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <div class="toolbar">
                <div class="toolbar-left">
                    <button class="btn btn-primary">
                        <span>➕</span>
                        <span>新建 Prompt</span>
                    </button>
                    <button class="btn btn-secondary">
                        <span>📤</span>
                        <span>导出</span>
                    </button>
                    <button class="btn btn-secondary">
                        <span>🔄</span>
                        <span>同步</span>
                    </button>
                </div>
                <div class="toolbar-right">
                    <button class="btn btn-secondary">
                        <span>⚙️</span>
                        <span>设置</span>
                    </button>
                </div>
            </div>

            <div class="content-area">
                <div class="content-grid">
                    <!-- 编辑区域 -->
                    <div class="editor-section">
                        <h2 class="section-header">编辑 Prompt</h2>
                        
                        <div class="form-group">
                            <label class="form-label">标题</label>
                            <input type="text" class="form-input" value="专业邮件回复助手">
                        </div>

                        <div class="form-group">
                            <label class="form-label">描述</label>
                            <textarea class="form-textarea" placeholder="描述这个 Prompt 的用途...">帮助用户撰写专业、得体的商务邮件回复，确保语气恰当、内容完整。</textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Prompt 内容</label>
                            <textarea class="form-textarea" style="min-height: 300px;" placeholder="输入你的 Prompt...">你是一位专业的商务邮件撰写助手。请帮助用户撰写清晰、专业、礼貌的邮件回复。

在撰写时请注意：
1. 保持专业但友好的语气
2. 结构清晰，重点突出
3. 语言简洁，避免冗余
4. 适当使用商务礼仪用语

请根据用户提供的邮件内容和回复要求，生成合适的邮件回复。</textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">标签</label>
                            <div class="tag-input-container">
                                <span class="tag">
                                    邮件
                                    <span class="tag-remove">×</span>
                                </span>
                                <span class="tag">
                                    商务
                                    <span class="tag-remove">×</span>
                                </span>
                                <span class="tag">
                                    助手
                                    <span class="tag-remove">×</span>
                                </span>
                                <input type="text" class="tag-input" placeholder="添加标签...">
                            </div>
                        </div>
                    </div>

                    <!-- 属性面板 -->
                    <div class="properties-panel">
                        <h2 class="section-header">属性设置</h2>
                        
                        <div class="property-item">
                            <span class="property-label">公开分享</span>
                            <div class="toggle-switch active"></div>
                        </div>

                        <div class="property-item">
                            <span class="property-label">允许复制</span>
                            <div class="toggle-switch"></div>
                        </div>

                        <div class="property-item">
                            <span class="property-label">自动保存</span>
                            <div class="toggle-switch active"></div>
                        </div>

                        <div class="property-item">
                            <span class="property-label">创建时间</span>
                            <span class="property-value">2025年6月18日 23:04</span>
                        </div>

                        <div class="property-item">
                            <span class="property-label">更新时间</span>
                            <span class="property-value">2025年6月19日 20:07</span>
                        </div>

                        <div class="property-item">
                            <span class="property-label">使用次数</span>
                            <span class="property-value">3</span>
                        </div>

                        <div class="property-item">
                            <span class="property-label">最后使用</span>
                            <span class="property-value">2分钟前</span>
                        </div>

                        <div style="margin-top: 24px; padding-top: 24px; border-top: 1px solid #f0f2f5;">
                            <button class="btn btn-primary" style="width: 100%; margin-bottom: 12px;">
                                💾 保存更改
                            </button>
                            <button class="btn btn-secondary" style="width: 100%;">
                                🗑️ 删除 Prompt
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 切换开关
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', function() {
                this.classList.toggle('active');
            });
        });

        // 标签删除
        document.querySelectorAll('.tag-remove').forEach(remove => {
            remove.addEventListener('click', function() {
                this.parentElement.remove();
            });
        });

        // Prompt 项目切换
        document.querySelectorAll('.prompt-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.prompt-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 标签输入
        const tagInput = document.querySelector('.tag-input');
        tagInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && this.value.trim()) {
                const tag = document.createElement('span');
                tag.className = 'tag';
                tag.innerHTML = `${this.value}<span class="tag-remove">×</span>`;
                this.parentElement.insertBefore(tag, this);
                this.value = '';
                
                tag.querySelector('.tag-remove').addEventListener('click', function() {
                    this.parentElement.remove();
                });
            }
        });
    </script>
</body>
</html>