import React, { forwardRef, useState } from 'react';
import { Edit3, Trash2, Eye, MoreHorizontal } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { cn } from '../../lib/utils';

const PromptItem = forwardRef(({
  prompt,
  isSelected,
  folderName,
  searchQuery,
  onClick,
  onDoubleClick,
  onEdit,
  onDelete,
  onDragStart,
  onDragEnd,
  onDragOver,
  onDrop,
  isDragging,
  isDragOver
}, ref) => {
  const [dragStarted, setDragStarted] = useState(false);
  
  // 高亮搜索关键词
  const highlightText = (text, query) => {
    if (!query || !text) return text;
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="highlight">{part}</mark>
      ) : part
    );
  };

  const handleClick = () => {
    onClick();
  };

  const handleDoubleClick = () => {
    onDoubleClick();
  };

  // 拖拽事件处理
  const handleDragStart = (e) => {
    setDragStarted(true);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', prompt.id);

    // 设置拖拽图像
    const dragImage = e.currentTarget.cloneNode(true);
    dragImage.style.opacity = '0.8';
    dragImage.style.transform = 'rotate(5deg)';
    document.body.appendChild(dragImage);
    e.dataTransfer.setDragImage(dragImage, 0, 0);

    // 清理临时元素
    setTimeout(() => {
      document.body.removeChild(dragImage);
    }, 0);

    if (onDragStart) {
      onDragStart(prompt, e);
    }
  };

  const handleDragEnd = (e) => {
    setDragStarted(false);
    if (onDragEnd) {
      onDragEnd(prompt, e);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    if (onDragOver) {
      onDragOver(prompt, e);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const draggedId = e.dataTransfer.getData('text/plain');
    if (onDrop && draggedId !== prompt.id) {
      onDrop(draggedId, prompt, e);
    }
  };

  return (
    <div
      ref={ref}
      className={cn(
        "group relative prompt-card cursor-pointer transition-all duration-200",
        "mx-3 mb-3 p-4 bg-white/80 hover:bg-white border border-slate-200/60 rounded-xl hover:shadow-md hover:border-blue-200",
        isSelected && "ring-2 ring-blue-400 ring-opacity-50 bg-blue-50/50 border-blue-300",
        isDragging && "opacity-50 rotate-1 shadow-xl z-50 scale-105",
        isDragOver && "border-t-2 border-t-blue-400 bg-blue-50/80 shadow-lg"
      )}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      draggable={true}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <div className="flex items-start justify-between mb-3">
        <h3 className="text-base font-semibold text-slate-800 truncate leading-tight flex-1 mr-3">
          {highlightText(prompt.title, searchQuery)}
        </h3>
        <div className="flex items-center gap-2">
          {folderName && (
            <Badge variant="secondary" className="text-xs bg-slate-100 text-slate-600 border-slate-200 rounded-full px-2 py-1">
              {highlightText(folderName, searchQuery)}
            </Badge>
          )}
          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-all duration-200">
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 rounded-lg hover:bg-blue-50 hover:text-blue-600 hover:scale-110 transition-all duration-200"
              onClick={(e) => {
                e.stopPropagation();
                if (onEdit) {
                  onEdit(prompt);
                }
              }}
              title="编辑"
            >
              <Edit3 className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 rounded-lg hover:bg-red-50 hover:text-red-600 hover:scale-110 transition-all duration-200"
              onClick={(e) => {
                e.stopPropagation();
                if (onDelete && window.confirm('确定要删除这个 Prompt 吗？')) {
                  onDelete(prompt.id);
                }
              }}
              title="删除"
            >
              <Trash2 className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 rounded-lg hover:bg-green-50 hover:text-green-600 hover:scale-110 transition-all duration-200"
              onClick={(e) => {
                e.stopPropagation();
                // 这里可以添加查看详情的逻辑
              }}
              title="查看详情"
            >
              <Eye className="h-3.5 w-3.5" />
            </Button>
          </div>
        </div>
      </div>

      {prompt.description && (
        <p className="text-sm text-slate-600 mb-3 leading-relaxed overflow-hidden" style={{display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical'}}>
          {highlightText(prompt.description, searchQuery)}
        </p>
      )}

      <div className="flex items-center justify-between">
        <div className="text-xs text-slate-500">
          {prompt.usageCount > 0 && (
            <span className="bg-slate-100 px-2 py-1 rounded-full font-medium">
              使用 {prompt.usageCount} 次
            </span>
          )}
        </div>
        <div className="text-xs text-slate-400">
          {prompt.updatedAt && new Date(prompt.updatedAt).toLocaleDateString()}
        </div>
      </div>
    </div>
  );
});

PromptItem.displayName = 'PromptItem';

export default PromptItem;
