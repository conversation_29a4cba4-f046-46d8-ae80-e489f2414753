import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Textarea } from '../../components/ui/textarea';
import { Select } from '../../components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';

function PromptEditor({ 
  prompt, 
  folders, 
  onSave, 
  onCancel, 
  onDelete 
}) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    folderId: ''
  });
  const [errors, setErrors] = useState({});
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);

  const isEditing = !!prompt;

  useEffect(() => {
    if (prompt) {
      setFormData({
        title: prompt.title || '',
        description: prompt.description || '',
        content: prompt.content || '',
        folderId: prompt.folderId || ''
      });
    } else {
      setFormData({
        title: '',
        description: '',
        content: '',
        folderId: ''
      });
    }
    setErrors({});
  }, [prompt]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = '标题不能为空';
    } else if (formData.title.length > 100) {
      newErrors.title = '标题长度不能超过100个字符';
    }
    
    if (!formData.content.trim()) {
      newErrors.content = '内容不能为空';
    }
    
    if (formData.description && formData.description.length > 200) {
      newErrors.description = '描述长度不能超过200个字符';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }
    
    setSaving(true);
    
    try {
      const promptData = {
        ...formData,
        title: formData.title.trim(),
        description: formData.description.trim(),
        content: formData.content.trim(),
        folderId: formData.folderId || null
      };
      
      if (isEditing) {
        promptData.id = prompt.id;
      }
      
      await onSave(promptData);
    } catch (error) {
      console.error('Error saving prompt:', error);
      setErrors({ general: '保存失败，请重试' });
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!isEditing || !prompt) return;
    
    if (!window.confirm('确定要删除这个 Prompt 吗？此操作不可撤销。')) {
      return;
    }
    
    setDeleting(true);
    
    try {
      await onDelete(prompt.id);
    } catch (error) {
      console.error('Error deleting prompt:', error);
      setErrors({ general: '删除失败，请重试' });
    } finally {
      setDeleting(false);
    }
  };

  const handleKeyDown = (event) => {
    if (event.key === 'Escape') {
      onCancel();
    } else if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleSave();
    }
  };

  return (
    <Card className="w-full max-w-2xl max-h-[90vh] overflow-hidden shadow-lg" onKeyDown={handleKeyDown}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg">
          {isEditing ? '编辑 Prompt' : '添加 Prompt'}
        </CardTitle>
        <Button variant="ghost" size="icon" onClick={onCancel} className="h-6 w-6">
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>

      <CardContent className="overflow-y-auto space-y-4">
        {errors.general && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-md p-3 text-sm text-destructive">
            {errors.general}
          </div>
        )}

        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground" htmlFor="prompt-title">
            标题 <span className="text-destructive">*</span>
          </label>
          <Input
            id="prompt-title"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            placeholder="输入 Prompt 标题"
            maxLength={100}
            className={errors.title ? 'border-destructive' : ''}
          />
          {errors.title && (
            <div className="text-sm text-destructive">{errors.title}</div>
          )}
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground" htmlFor="prompt-description">
            描述
          </label>
          <Input
            id="prompt-description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="输入 Prompt 描述（可选）"
            maxLength={200}
            className={errors.description ? 'border-destructive' : ''}
          />
          {errors.description && (
            <div className="text-sm text-destructive">{errors.description}</div>
          )}
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground" htmlFor="prompt-folder">
            文件夹
          </label>
          <Select
            value={formData.folderId}
            onChange={(e) => handleInputChange('folderId', e.target.value)}
          >
            <option value="">无文件夹</option>
            {folders.map(folder => (
              <option key={folder.id} value={folder.id}>
                {folder.name}
              </option>
            ))}
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground" htmlFor="prompt-content">
            内容 <span className="text-destructive">*</span>
          </label>
          <Textarea
            id="prompt-content"
            value={formData.content}
            onChange={(e) => handleInputChange('content', e.target.value)}
            placeholder="输入 Prompt 内容"
            rows={8}
            className={`font-mono ${errors.content ? 'border-destructive' : ''}`}
          />
          {errors.content && (
            <div className="text-sm text-destructive">{errors.content}</div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div>
            {isEditing && (
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={deleting}
              >
                {deleting ? '删除中...' : '删除'}
              </Button>
            )}
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={onCancel}
              disabled={saving || deleting}
            >
              取消
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving || deleting}
            >
              {saving ? '保存中...' : '保存'}
            </Button>
          </div>
        </div>

        <div className="flex gap-4 text-xs text-muted-foreground pt-2 border-t">
          <span className="flex items-center gap-1">
            <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Ctrl+Enter</kbd>
            保存
          </span>
          <span className="flex items-center gap-1">
            <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Esc</kbd>
            取消
          </span>
        </div>
      </CardContent>
    </Card>
  );
}

export default PromptEditor;
