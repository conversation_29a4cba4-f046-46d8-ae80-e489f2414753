## **产品需求文档 (PRD): Prompt 管理 Chrome 插件**

**1. 引言**

*   **项目名称：** 优雅 Prompt 管理器 (暂定)
*   **项目目标：** 开发一款 Chrome 浏览器插件，为用户提供一个优雅、高效的 Prompt 管理方案，解决用户在日常工作中需要管理和使用大量 Prompt 的痛点。
*   **用户痛点：** 用户拥有大量 Prompt，缺乏便捷的管理和快速调用机制，影响工作效率。

**2. 功能需求**

*   **2.1. 面板唤出与交互**
    *   **唤出方式：**
        *   快捷键：`Alt+S` 或 `Alt+W` (可自定义者更佳，初期固定亦可)。
        *   鼠标触发：鼠标移动到浏览器右侧边缘时自动唤出。
    *   **面板形态：** 一个固定在浏览器视窗右侧的方形面板。
    *   **初始状态：** 面板唤出后，光标自动定位到面板内的搜索框中。

*   **2.2. 搜索功能**
    *   **实时搜索：** 在搜索框输入内容后，无需按下回车，实时根据关键字筛选 Prompt 列表。
    *   **匹配字段：** 搜索范围应包括 Prompt 的标题、描述、内容以及其所属文件夹的名称。
    *   **搜索体验增强：**
        *   **高亮匹配关键词：** 在搜索结果的 Prompt 标题和描述中，将用户输入的搜索关键词进行高亮显示。
        *   **文件夹路径提示：** 在搜索结果列表中，清晰展示每个 Prompt 所属的文件夹路径（例如：“工作/邮件模板”），帮助用户在相似结果中快速定位。

*   **2.3. Prompt 列表与选中**
    *   **列表展示：** 搜索框下方为 Prompt 列表，每个列表项展示 Prompt 的标题和描述。
    *   **默认选中：** 面板唤出或搜索结果更新后，默认选中列表中的第一个 Prompt。
    *   **选中方式：**
        *   键盘操作：支持使用小键盘方向键（上下）或 `Tab` 键在 Prompt 列表中进行切换选中。
    *   **内容片段预览：** 当用户通过键盘选中某个 Prompt 时，在面板的特定区域（例如，列表项下方或面板右侧）动态展示该 Prompt 内容的前几行或核心摘要，以便用户快速预览。

*   **2.4. Prompt 上屏**
    *   **触发方式：** 在 Prompt 列表中选中某个 Prompt 后，按下 `Enter` 键。
    *   **上屏行为：** 将选中 Prompt 的完整内容追加到当前网页活动输入框的末尾。
    *   **后续行为：** 上屏成功后，光标自动回到该活动输入框中，插件面板可选择隐藏或保持显示状态（优先考虑隐藏，以减少干扰）。

*   **2.5. “#”快捷输入与自动补全**
    *   **触发方式：** 用户在网页的任意输入框中输入“#”字符后，紧接着输入关键词。
    *   **补全逻辑：** 根据“#”后输入的关键词，实时搜索匹配的 Prompt（匹配字段同 2.2）。
    *   **补全提示：** 在输入框附近以下拉列表或类似形式展示匹配到的 Prompt 标题作为预选项。
    *   **上屏确认：** 用户可以通过方向键选择补全提示中的 Prompt，按下 `Enter` 键确认。确认后，选中的 Prompt 内容替换掉从“#”开始到光标处的所有内容，并直接上屏。
    *   **交互优化：** 确保此处的 `Enter` 键优先作用于选中补全项，避免与输入框本身的回车发送消息功能冲突（参考 Cursor 编辑器的实现方式）。

*   **2.6. Prompt 管理**
    *   **Prompt 字段：**
        *   标题 (必填)
        *   描述 (选填，用户可自定义，或由系统截取内容前 N 个字符生成；未来可考虑 AI 总结)
        *   内容 (必填，即 Prompt 本体)
        *   所属文件夹 (可选，默认为无文件夹或根目录)
        *   (未来可考虑加入“索引词/标签”字段，以增强搜索能力)
    *   **添加 Prompt：** 面板内提供“添加 Prompt”按钮，点击后弹出/跳转至添加界面。
    *   **编辑 Prompt：** 在 Prompt 列表的每个 Prompt 项右侧提供“编辑”按钮，点击后弹出/跳转至编辑界面。添加和编辑可共用同一界面，仅标题不同。
    *   **删除 Prompt：** 在编辑界面或 Prompt 列表项上提供删除功能（需二次确认）。
    *   **文件夹管理：**
        *   用户可以自定义文件夹名称。
        *   文件夹不可嵌套（即只支持一级文件夹）。
        *   支持通过拖拽方式移动 Prompt 到不同文件夹。
        *   支持通过拖拽方式调整文件夹在列表中的顺序。
        *   支持通过拖拽方式调整同一文件夹内 Prompt 的顺序。

*   **2.7. 数据管理**
    *   **数据导出：** 提供将所有 Prompts 及文件夹结构导出为文件的功能（例如 JSON 格式），方便用户备份。
    *   **数据导入：** 提供通过导入文件恢复 Prompts 及文件夹结构的功能。

**3. 非功能需求 (初步)**

*   **性能：** 插件响应应迅速，搜索和列表加载不应有明显卡顿。
*   **易用性：** 界面简洁直观，交互符合用户习惯。
*   **兼容性：** 兼容主流现代浏览器（主要指 Chrome 最新版及其前几个版本）。

**4. 未来展望 (记录备忘)**

*   描述自动生成：利用 AI 技术总结 Prompt 内容生成描述。
*   Prompt 模板化与占位符功能。
*   使用频率统计与智能排序/推荐。
*   更高级的快捷键自定义。
*   云同步功能。

    