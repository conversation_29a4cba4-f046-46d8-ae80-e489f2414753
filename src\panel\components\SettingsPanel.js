import React, { useState, useEffect } from 'react';
import { X, Plus, Edit, Trash2, Download, Upload, Folder } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';

function SettingsPanel({ onClose, onDataImported }) {
  const [importing, setImporting] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState(''); // 'success' or 'error'
  const [folders, setFolders] = useState([]);
  const [showFolderEditor, setShowFolderEditor] = useState(false);
  const [editingFolder, setEditingFolder] = useState(null);
  const [folderName, setFolderName] = useState('');

  // 初始化数据
  useEffect(() => {
    loadFolders();
  }, []);

  // 加载文件夹数据
  const loadFolders = async () => {
    try {
      const response = await sendMessageToBackground({ action: 'get-folders' });
      setFolders(response || []);
    } catch (error) {
      console.error('Error loading folders:', error);
    }
  };

  // 导出数据
  const handleExport = async () => {
    try {
      setExporting(true);
      setMessage('');
      
      const response = await sendMessageToBackground({ action: 'export-data' });
      
      if (response.success) {
        // 创建下载链接
        const blob = new Blob([JSON.stringify(response.data, null, 2)], {
          type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `prompt-manager-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        setMessage('数据导出成功！');
        setMessageType('success');
      } else {
        setMessage(`导出失败：${response.error}`);
        setMessageType('error');
      }
    } catch (error) {
      console.error('Export error:', error);
      setMessage(`导出失败：${error.message}`);
      setMessageType('error');
    } finally {
      setExporting(false);
    }
  };

  // 导入数据
  const handleImport = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        setImporting(true);
        setMessage('');
        
        const data = JSON.parse(e.target.result);
        const response = await sendMessageToBackground({ 
          action: 'import-data', 
          data 
        });
        
        if (response.success) {
          setMessage(response.message || '数据导入成功！');
          setMessageType('success');
          
          // 通知父组件数据已更新
          if (onDataImported) {
            onDataImported();
          }
        } else {
          setMessage(`导入失败：${response.error}`);
          setMessageType('error');
        }
      } catch (error) {
        console.error('Import error:', error);
        setMessage(`导入失败：${error.message}`);
        setMessageType('error');
      } finally {
        setImporting(false);
        // 清空文件输入
        event.target.value = '';
      }
    };
    
    reader.readAsText(file);
  };

  // 向background script发送消息
  const sendMessageToBackground = (message) => {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    });
  };

  // 清除消息
  const clearMessage = () => {
    setMessage('');
    setMessageType('');
  };

  // 显示添加文件夹编辑器
  const showAddFolderEditor = () => {
    setEditingFolder(null);
    setFolderName('');
    setShowFolderEditor(true);
  };

  // 显示编辑文件夹编辑器
  const showEditFolderEditor = (folder) => {
    setEditingFolder(folder);
    setFolderName(folder.name);
    setShowFolderEditor(true);
  };

  // 隐藏文件夹编辑器
  const hideFolderEditor = () => {
    setShowFolderEditor(false);
    setEditingFolder(null);
    setFolderName('');
  };

  // 保存文件夹
  const handleSaveFolder = async () => {
    if (!folderName.trim()) {
      setMessage('文件夹名称不能为空');
      setMessageType('error');
      return;
    }

    try {
      const folderData = {
        ...editingFolder,
        name: folderName.trim()
      };

      const response = await sendMessageToBackground({
        action: 'save-folder',
        folder: folderData
      });

      if (response.success) {
        setMessage(editingFolder ? '文件夹更新成功！' : '文件夹创建成功！');
        setMessageType('success');
        hideFolderEditor();
        await loadFolders();
      } else {
        setMessage(`保存失败：${response.error}`);
        setMessageType('error');
      }
    } catch (error) {
      console.error('Error saving folder:', error);
      setMessage(`保存失败：${error.message}`);
      setMessageType('error');
    }
  };

  // 删除文件夹
  const handleDeleteFolder = async (folder) => {
    if (!window.confirm(`确定要删除文件夹"${folder.name}"吗？此操作不可撤销。`)) {
      return;
    }

    try {
      const response = await sendMessageToBackground({
        action: 'delete-folder',
        folderId: folder.id
      });

      if (response.success) {
        setMessage('文件夹删除成功！');
        setMessageType('success');
        await loadFolders();
      } else {
        setMessage(`删除失败：${response.error}`);
        setMessageType('error');
      }
    } catch (error) {
      console.error('Error deleting folder:', error);
      setMessage(`删除失败：${error.message}`);
      setMessageType('error');
    }
  };

  return (
    <Card className="w-full max-w-2xl max-h-[90vh] overflow-hidden shadow-lg">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg">设置</CardTitle>
        <Button variant="ghost" size="icon" onClick={onClose} className="h-6 w-6">
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>

      <CardContent className="overflow-y-auto space-y-6">
        {/* 文件夹管理 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-base font-semibold text-foreground">文件夹管理</h3>
              <p className="text-sm text-muted-foreground">管理您的 Prompt 文件夹</p>
            </div>
            <Button
              onClick={showAddFolderEditor}
              size="sm"
              className="gap-2"
            >
              <Plus className="h-4 w-4" />
              添加文件夹
            </Button>
          </div>

          <div className="border rounded-lg overflow-hidden">
            {folders.map(folder => (
              <div key={folder.id} className="flex items-center justify-between p-3 border-b last:border-b-0 hover:bg-muted/50 transition-colors">
                <div className="flex items-center gap-3">
                  <Folder className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="font-medium text-sm text-foreground">{folder.name}</div>
                    <div className="text-xs text-muted-foreground">
                      创建于 {new Date(folder.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => showEditFolderEditor(folder)}
                    title="编辑文件夹"
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6 text-destructive hover:text-destructive"
                    onClick={() => handleDeleteFolder(folder)}
                    title="删除文件夹"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
            {folders.length === 0 && (
              <div className="p-6 text-center text-sm text-muted-foreground">
                暂无文件夹，点击上方按钮添加第一个文件夹
              </div>
            )}
          </div>
        </div>

        {/* 数据管理 */}
        <div className="space-y-4">
          <div>
            <h3 className="text-base font-semibold text-foreground mb-2">数据管理</h3>
            <p className="text-sm text-muted-foreground">导入导出您的 Prompt 数据</p>
          </div>

          <div className="grid gap-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium text-sm text-foreground">导出数据</h4>
                <p className="text-xs text-muted-foreground">将所有 Prompt 和文件夹导出为 JSON 文件</p>
              </div>
              <Button
                onClick={handleExport}
                disabled={exporting}
                size="sm"
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                {exporting ? '导出中...' : '导出'}
              </Button>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium text-sm text-foreground">导入数据</h4>
                <p className="text-xs text-muted-foreground">从 JSON 文件导入 Prompt 和文件夹（会覆盖现有数据）</p>
              </div>
              <div>
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImport}
                  disabled={importing}
                  className="hidden"
                  id="import-file"
                />
                <Button
                  asChild
                  disabled={importing}
                  size="sm"
                  variant="outline"
                  className="gap-2"
                >
                  <label htmlFor="import-file" className="cursor-pointer">
                    <Upload className="h-4 w-4" />
                    {importing ? '导入中...' : '选择文件'}
                  </label>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* 消息提示 */}
        {message && (
          <div className={`p-3 rounded-lg border flex items-center justify-between ${
            messageType === 'success'
              ? 'bg-green-50 border-green-200 text-green-800'
              : 'bg-red-50 border-red-200 text-red-800'
          }`}>
            <span className="text-sm">{message}</span>
            <Button variant="ghost" size="icon" onClick={clearMessage} className="h-6 w-6">
              <X className="h-3 w-3" />
            </Button>
          </div>
        )}
      </CardContent>


      {/* 文件夹编辑器模态框 */}
      {showFolderEditor && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md shadow-lg">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
              <CardTitle className="text-base">
                {editingFolder ? '编辑文件夹' : '添加文件夹'}
              </CardTitle>
              <Button variant="ghost" size="icon" onClick={hideFolderEditor} className="h-6 w-6">
                <X className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="folder-name" className="text-sm font-medium text-foreground">
                  文件夹名称
                </label>
                <Input
                  id="folder-name"
                  value={folderName}
                  onChange={(e) => setFolderName(e.target.value)}
                  placeholder="请输入文件夹名称"
                  autoFocus
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSaveFolder();
                    } else if (e.key === 'Escape') {
                      hideFolderEditor();
                    }
                  }}
                />
              </div>
              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline" onClick={hideFolderEditor}>
                  取消
                </Button>
                <Button onClick={handleSaveFolder}>
                  {editingFolder ? '更新' : '创建'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </Card>
  );
}

export default SettingsPanel;
