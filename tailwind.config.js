/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  safelist: [
    // 确保我们使用的渐变和颜色类被包含
    'bg-gradient-to-br',
    'bg-gradient-to-r',
    'bg-gradient-to-b',
    'from-slate-50',
    'to-blue-50/30',
    'from-slate-800',
    'to-slate-600',
    'from-blue-500',
    'to-purple-600',
    'from-blue-600',
    'to-purple-700',
    'from-blue-50',
    'to-purple-50',
    'from-white/60',
    'to-slate-50/80',
    'from-amber-50',
    'to-yellow-50',
    'text-slate-50',
    'text-slate-100',
    'text-slate-200',
    'text-slate-300',
    'text-slate-400',
    'text-slate-500',
    'text-slate-600',
    'text-slate-700',
    'text-slate-800',
    'text-slate-900',
    'bg-slate-50',
    'bg-slate-100',
    'bg-slate-200',
    'bg-white/80',
    'bg-white/60',
    'bg-white/50',
    'bg-white/30',
    'border-slate-200/60',
    'border-slate-200',
    'border-slate-300/60',
    'border-blue-200',
    'border-blue-400',
    'border-amber-200',
    'rounded-xl',
    'rounded-lg',
    'shadow-sm',
    'shadow-md',
    'shadow-lg',
    'shadow-xl',
    'backdrop-blur-sm',
    'ring-1',
    'ring-blue-200/50',
    'ring-amber-200/50',
    'animate-pulse',
    'hover:shadow-md',
    'hover:shadow-lg',
    'focus:ring-2',
    'focus:ring-blue-500/20',
    'focus:border-blue-400'
  ],
  theme: {
    extend: {
      colors: {
        // 保留 shadcn/ui 的颜色系统
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [],
}
